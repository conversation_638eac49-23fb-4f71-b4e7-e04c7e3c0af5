["test_consolidation.py::TestConsolidationLS2Updates::test_function_signature_accepts_config_dict", "test_consolidation.py::TestConsolidationLS2Updates::test_invalid_json_raises_webodm_data_error", "test_consolidation.py::TestConsolidationLS2Updates::test_logging_integration", "test_consolidation.py::TestConsolidationLS2Updates::test_missing_config_key_raises_pipeline_config_error", "test_consolidation.py::TestConsolidationLS2Updates::test_missing_features_key_raises_webodm_data_error", "test_consolidation.py::TestConsolidationLS2Updates::test_missing_shots_file_raises_input_data_error", "test_consolidation.py::TestConsolidationLS2Updates::test_successful_consolidation_with_valid_data", "test_georeferencing.py::TestDSMHelperFunctions::test_create_ray_dsm_difference_function", "test_georeferencing.py::TestDSMHelperFunctions::test_find_dsm_entry_point_no_entry", "test_georeferencing.py::TestDSMHelperFunctions::test_find_dsm_entry_point_success", "test_georeferencing.py::TestDSMHelperFunctions::test_get_dsm_height_at_point_nodata_value", "test_georeferencing.py::TestDSMHelperFunctions::test_get_dsm_height_at_point_outside_bounds", "test_georeferencing.py::TestDSMHelperFunctions::test_get_dsm_height_at_point_valid", "test_georeferencing.py::TestDSMIntersection::test_ray_encounters_nodata_at_sensor_xy", "test_georeferencing.py::TestDSMIntersection::test_ray_exits_dsm_bounds_xy", "test_georeferencing.py::TestDSMIntersection::test_ray_marching_max_distance_exceeded", "test_georeferencing.py::TestDSMIntersection::test_ray_marching_with_sloped_surface", "test_georeferencing.py::TestDSMIntersection::test_ray_misses_dsm_points_away", "test_georeferencing.py::TestDSMIntersection::test_ray_starts_below_points_up", "test_georeferencing.py::TestDSMIntersection::test_successful_intersection_from_above", "test_georeferencing.py::TestGeoreferencingLS2Updates::test_function_signature_accepts_config_dict", "test_georeferencing.py::TestGeoreferencingLS2Updates::test_missing_config_key_raises_pipeline_config_error", "test_georeferencing.py::TestHelperFunctions::test_parse_hsi_header_missing_file", "test_georeferencing.py::TestHelperFunctions::test_parse_hsi_header_missing_lines", "test_georeferencing.py::TestHelperFunctions::test_parse_hsi_header_missing_samples", "test_georeferencing.py::TestHelperFunctions::test_parse_hsi_header_no_lever_arm", "test_georeferencing.py::TestHelperFunctions::test_parse_hsi_header_success", "test_georeferencing.py::TestHelperFunctions::test_parse_sensor_model_missing_file", "test_georeferencing.py::TestHelperFunctions::test_parse_sensor_model_success", "test_georeferencing.py::TestHelperFunctions::test_parse_sensor_model_too_few_entries", "test_georeferencing.py::TestHelperFunctions::test_parse_sensor_model_too_many_entries", "test_georeferencing.py::TestLS5IncreasedCoverage::test_parse_hsi_header_general_exception", "test_georeferencing.py::TestLS5IncreasedCoverage::test_parse_hsi_header_invalid_lever_arm_format", "test_georeferencing.py::TestLS5IncreasedCoverage::test_parse_sensor_model_fallback_to_two_column_format", "test_georeferencing.py::TestLS5IncreasedCoverage::test_parse_sensor_model_truncation_warning", "test_georeferencing.py::TestLS5IncreasedCoverage::test_run_georeferencing_dsm_file_not_found", "test_georeferencing.py::TestLS5IncreasedCoverage::test_run_georeferencing_missing_poses_file", "test_georeferencing.py::TestLS5IncreasedCoverage::test_run_georeferencing_pose_line_mismatch", "test_georeferencing.py::TestLS5LeverArmParsing::test_parse_hsi_header_lever_arm_both_keys_different_values", "test_georeferencing.py::TestLS5LeverArmParsing::test_parse_hsi_header_lever_arm_both_keys_same_values", "test_georeferencing.py::TestLS5LeverArmParsing::test_parse_hsi_header_lever_arm_key_only", "test_georeferencing.py::TestLS5LeverArmParsing::test_parse_hsi_header_lever_arm_offset_only", "test_georeferencing.py::TestLS5PerformanceOptimizations::test_adaptive_step_sizing_behavior", "test_georeferencing.py::TestLS5PerformanceOptimizations::test_bounds_checking_optimization", "test_georeferencing.py::TestLS5PerformanceOptimizations::test_optimized_ray_calculation_with_unpacked_components", "test_georeferencing.py::TestLS5PerformanceOptimizations::test_performance_with_multiple_rays", "test_georeferencing.py::TestLS5SensorModelLogging::test_parse_sensor_model_angles_in_degrees_with_enhanced_warning", "test_georeferencing.py::TestLS5SensorModelLogging::test_parse_sensor_model_angles_in_radians_no_warning", "test_georeferencing.py::TestLS5SensorModelLogging::test_parse_sensor_model_mixed_angles_trigger_degrees", "test_georeferencing.py::TestLS5SimplifiedBrentqHandling::test_brentq_handling_no_sign_change", "test_georeferencing.py::TestLS5SimplifiedBrentqHandling::test_brentq_handling_with_nan_endpoints", "test_georeferencing.py::TestLS5SimplifiedBrentqHandling::test_brentq_handling_with_valid_bracket", "test_georeferencing.py::TestLS6DSMPathResolution::test_dsm_path_resolution_absolute_path_unchanged", "test_georeferencing.py::TestLS6DSMPathResolution::test_dsm_path_resolution_relative_to_config", "test_georeferencing.py::TestLS6VectorizedPathFix::test_run_georeferencing_flat_plane_vectorized_invocation", "test_georeferencing.py::TestLS6VectorizedPathFix::test_run_georeferencing_flat_plane_vectorized_result_processing", "test_georeferencing.py::TestLoggingIntegration::test_logging_integration", "test_georeferencing.py::TestSensorModelParsing::test_parse_sensor_model_angles_in_degrees", "test_georeferencing.py::TestSensorModelParsing::test_parse_sensor_model_angles_in_radians", "test_georeferencing.py::TestVectorizationIntegration::test_vectorized_processing_called_for_flat_plane", "test_georeferencing.py::TestVectorizedExceptionHandling::test_invalid_quaternion_handling_in_vectorized_function", "test_georeferencing.py::TestVectorizedExceptionHandling::test_run_georeferencing_fallback_on_pose_transformation_error", "test_georeferencing.py::TestVectorizedExceptionHandling::test_vectorized_processing_specific_exception_fallback", "test_lever_arm.py::TestIntegrationLeverArm::test_lever_arm_integration_scenario", "test_lever_arm.py::TestIntegrationLeverArm::test_lever_arm_precision", "test_lever_arm.py::TestLeverArmDetermination::test_dead_code_coverage_line_75", "test_lever_arm.py::TestLeverArmDetermination::test_lever_arm_both_zero", "test_lever_arm.py::TestLeverArmDetermination::test_lever_arm_config_override", "test_lever_arm.py::TestLeverArmDetermination::test_lever_arm_hdr_only", "test_lever_arm.py::TestLeverArmDetermination::test_lever_arm_hdr_with_zero_config", "test_lever_arm.py::TestLeverArmDetermination::test_lever_arm_none_inputs", "test_lever_arm.py::TestLeverArmDetermination::test_warning_zero_effective_arm_with_nonzero_hdr", "test_lever_arm.py::TestLeverArmValidation::test_invalid_lever_arm_shape", "test_lever_arm.py::TestLeverArmValidation::test_invalid_lever_arm_values", "test_lever_arm.py::TestLeverArmValidation::test_large_lever_arm_warning", "test_lever_arm.py::TestLeverArmValidation::test_none_lever_arm", "test_lever_arm.py::TestLeverArmValidation::test_valid_lever_arm", "test_main_pipeline.py::TestAdditionalErrorCoverage::test_consolidation_exception_coverage", "test_main_pipeline.py::TestAdditionalErrorCoverage::test_consolidation_failure_coverage", "test_main_pipeline.py::TestAdditionalErrorCoverage::test_georeferencing_exception_coverage", "test_main_pipeline.py::TestAdditionalErrorCoverage::test_georeferencing_failure_coverage", "test_main_pipeline.py::TestAdditionalErrorCoverage::test_plotting_exception_coverage", "test_main_pipeline.py::TestAdditionalErrorCoverage::test_rgb_creation_exception_coverage", "test_main_pipeline.py::TestAdditionalErrorCoverage::test_unexpected_exception_coverage", "test_main_pipeline.py::TestConfigurationLoading::test_load_config_with_unicode", "test_main_pipeline.py::TestConfigurationLoading::test_load_invalid_toml_syntax", "test_main_pipeline.py::TestConfigurationLoading::test_load_nonexistent_config", "test_main_pipeline.py::TestConfigurationLoading::test_load_valid_config", "test_main_pipeline.py::TestConfigurationPassing::test_config_object_immutability", "test_main_pipeline.py::TestConfigurationPassing::test_config_passed_to_submodules", "test_main_pipeline.py::TestLoggingIntegration::test_logging_messages", "test_main_pipeline.py::TestLoggingIntegration::test_logging_setup", "test_main_pipeline.py::TestPipelineOrchestration::test_pipeline_config_error", "test_main_pipeline.py::TestPipelineOrchestration::test_pipeline_failure_in_critical_step", "test_main_pipeline.py::TestPipelineOrchestration::test_pipeline_unexpected_error", "test_main_pipeline.py::TestPipelineOrchestration::test_pipeline_warning_in_optional_step", "test_main_pipeline.py::TestPipelineOrchestration::test_successful_pipeline_execution", "test_synchronization.py::TestHelperFunctions::test_convert_hsi_timestamp_to_ns", "test_synchronization.py::TestHelperFunctions::test_interpolate_pose_exact_match", "test_synchronization.py::TestHelperFunctions::test_interpolate_pose_extrapolation_after", "test_synchronization.py::TestHelperFunctions::test_interpolate_pose_extrapolation_before", "test_synchronization.py::TestHelperFunctions::test_interpolate_pose_interpolation", "test_synchronization.py::TestHelperFunctions::test_load_hsi_data_invalid_format", "test_synchronization.py::TestHelperFunctions::test_load_hsi_data_success", "test_synchronization.py::TestHelperFunctions::test_load_webodm_data_missing_columns", "test_synchronization.py::TestHelperFunctions::test_load_webodm_data_success", "test_synchronization.py::TestHelperFunctions::test_parse_hdr_file_missing_file", "test_synchronization.py::TestHelperFunctions::test_parse_hdr_file_success", "test_synchronization.py::TestLoggingIntegration::test_logging_integration", "test_synchronization.py::TestSynchronizationLS2Updates::test_function_signature_accepts_config_dict", "test_synchronization.py::TestSynchronizationLS2Updates::test_missing_config_key_raises_pipeline_config_error", "test_vectorized_georef.py::TestLS6VectorizedGeoreferencingCoverage::test_calculate_sensor_pixel_vectors_vectorized_boresight_center", "test_vectorized_georef.py::TestLS6VectorizedGeoreferencingCoverage::test_intersect_rays_with_horizontal_plane_vectorized_nadir_pointing", "test_vectorized_georef.py::TestLS6VectorizedGeoreferencingCoverage::test_intersect_rays_with_horizontal_plane_vectorized_ray_parallel_to_plane", "test_vectorized_georef.py::TestLS6VectorizedGeoreferencingCoverage::test_prepare_rotation_matrices_vectorized_90deg_x_rotation", "test_vectorized_georef.py::TestLS6VectorizedGeoreferencingCoverage::test_prepare_rotation_matrices_vectorized_identity", "test_vectorized_georef.py::TestLS6VectorizedGeoreferencingCoverage::test_process_hsi_line_vectorized_dsm_method_missing_interpolator", "test_vectorized_georef.py::TestLS6VectorizedGeoreferencingCoverage::test_process_hsi_line_vectorized_flat_plane_nadir_simple", "test_vectorized_georef.py::TestLS6VectorizedGeoreferencingCoverage::test_transform_vectors_to_world_vectorized_identity_rotations", "test_vectorized_georef.py::TestPerformanceBenchmark::test_log_vectorized_vs_iterative_performance", "test_vectorized_georef.py::TestPerformanceBenchmark::test_vectorized_vs_iterative_performance", "test_vectorized_georef.py::TestVectorizedFlatPlaneIntersection::test_flat_plane_intersection_backward_rays", "test_vectorized_georef.py::TestVectorizedFlatPlaneIntersection::test_flat_plane_intersection_no_intersection", "test_vectorized_georef.py::TestVectorizedFlatPlaneIntersection::test_flat_plane_intersection_simple", "test_vectorized_georef.py::TestVectorizedFlatPlaneIntersection::test_flat_plane_intersection_threshold", "test_vectorized_georef.py::TestVectorizedLineProcessing::test_process_hsi_line_flat_plane", "test_vectorized_georef.py::TestVectorizedLineProcessing::test_process_hsi_line_invalid_quaternion", "test_vectorized_georef.py::TestVectorizedSensorViewVectors::test_sensor_view_vectors_multiple_pixels", "test_vectorized_georef.py::TestVectorizedSensorViewVectors::test_sensor_view_vectors_single_pixel", "test_vectorized_georef.py::TestVectorizedSensorViewVectors::test_sensor_view_vectors_with_correction", "test_vectorized_georef.py::TestVectorizedSensorViewVectors::test_sensor_view_vectors_zero_norm_handling", "test_vectorized_georef.py::TestVectorizedWorldTransform::test_world_transform_identity", "test_vectorized_georef.py::TestVectorizedWorldTransform::test_world_transform_multiple_vectors", "test_vectorized_georef.py::TestVectorizedWorldTransform::test_world_transform_rotation"]
"""
Unit tests for logging configuration functionality.

This module tests the logging setup and configuration as specified in LS7_3 requirements.
"""

import pytest
import logging
import os
from pathlib import Path
from unittest.mock import patch, MagicMock

# Import the modules to test
from logging_config import setup_logging, get_logger


class TestSetupLogging:
    """Test cases for setup_logging function (LS7_3)."""
    
    def test_setup_logging_applies_config(self, tmp_path):
        """Test that setup_logging correctly configures the root logger."""
        # Arrange
        log_file_path = tmp_path / "test_pipeline.log"
        log_level_to_set = "DEBUG"
        
        # Act
        setup_logging(log_level=log_level_to_set, log_file=str(log_file_path))
        test_logger = get_logger("my_test_module")
        test_logger.debug("This is a debug message for setup_logging test.")
        test_logger.info("This is an info message for setup_logging test.")
        
        # Assert
        root_logger = logging.getLogger()
        assert root_logger.level == logging.DEBUG
        assert os.path.exists(log_file_path)
        
        with open(log_file_path, 'r') as f:
            log_content = f.read()
            assert "This is a debug message" in log_content
            assert "This is an info message" in log_content
    
    def test_setup_logging_creates_log_directory(self, tmp_path):
        """Test that setup_logging creates log directory if it doesn't exist."""
        # Arrange
        log_dir = tmp_path / "logs" / "subdir"
        log_file_path = log_dir / "test.log"
        
        # Ensure directory doesn't exist initially
        assert not log_dir.exists()
        
        # Act
        setup_logging(log_level="INFO", log_file=str(log_file_path))
        
        # Assert
        assert log_dir.exists()
        assert log_file_path.parent.exists()
    
    def test_setup_logging_different_log_levels(self, tmp_path):
        """Test setup_logging with different log levels."""
        # Test INFO level
        log_file_info = tmp_path / "info.log"
        setup_logging(log_level="INFO", log_file=str(log_file_info))
        
        root_logger = logging.getLogger()
        assert root_logger.level == logging.INFO
        
        # Test WARNING level
        log_file_warning = tmp_path / "warning.log"
        setup_logging(log_level="WARNING", log_file=str(log_file_warning))
        
        root_logger = logging.getLogger()
        assert root_logger.level == logging.WARNING
    
    def test_setup_logging_invalid_level_defaults_to_info(self, tmp_path):
        """Test that invalid log level defaults to INFO."""
        # Arrange
        log_file_path = tmp_path / "default.log"
        
        # Act
        setup_logging(log_level="INVALID_LEVEL", log_file=str(log_file_path))
        
        # Assert
        root_logger = logging.getLogger()
        assert root_logger.level == logging.INFO  # Should default to INFO


class TestGetLogger:
    """Test cases for get_logger function (LS7_3)."""
    
    def test_get_logger_retrieval(self):
        """Test that get_logger returns a logger instance with the correct name."""
        # Arrange
        logger_name = "test.module.special"
        
        # Act
        logger_instance = get_logger(logger_name)
        
        # Assert
        assert isinstance(logger_instance, logging.Logger)
        assert logger_instance.name == logger_name
    
    def test_get_logger_different_names(self):
        """Test that get_logger returns different loggers for different names."""
        # Arrange
        name1 = "module1"
        name2 = "module2"
        
        # Act
        logger1 = get_logger(name1)
        logger2 = get_logger(name2)
        
        # Assert
        assert logger1.name == name1
        assert logger2.name == name2
        assert logger1 is not logger2
    
    def test_get_logger_same_name_returns_same_instance(self):
        """Test that get_logger returns the same instance for the same name."""
        # Arrange
        logger_name = "same.module"
        
        # Act
        logger1 = get_logger(logger_name)
        logger2 = get_logger(logger_name)
        
        # Assert
        assert logger1 is logger2
        assert logger1.name == logger_name


class TestLoggingIntegration:
    """Integration tests for logging configuration."""
    
    def test_logging_configuration_persistence(self, tmp_path):
        """Test that logging configuration persists across multiple get_logger calls."""
        # Arrange
        log_file_path = tmp_path / "integration.log"
        setup_logging(log_level="DEBUG", log_file=str(log_file_path))
        
        # Act
        logger1 = get_logger("module1")
        logger2 = get_logger("module2")
        
        logger1.info("Message from module1")
        logger2.warning("Warning from module2")
        
        # Assert
        assert os.path.exists(log_file_path)
        with open(log_file_path, 'r') as f:
            log_content = f.read()
            assert "Message from module1" in log_content
            assert "Warning from module2" in log_content
            assert "module1" in log_content
            assert "module2" in log_content
    
    def test_logging_handlers_configuration(self, tmp_path):
        """Test that both console and file handlers are configured."""
        # Arrange
        log_file_path = tmp_path / "handlers.log"
        
        # Act
        setup_logging(log_level="INFO", log_file=str(log_file_path))
        
        # Assert
        root_logger = logging.getLogger()
        assert len(root_logger.handlers) >= 2  # Should have console and file handlers
        
        # Check handler types
        handler_types = [type(handler).__name__ for handler in root_logger.handlers]
        assert any("StreamHandler" in handler_type for handler_type in handler_types)
        assert any("RotatingFileHandler" in handler_type for handler_type in handler_types)


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v"])

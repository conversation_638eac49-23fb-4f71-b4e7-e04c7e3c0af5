# Test Specifications for Layer LS7

## Introduction

This document outlines the test specifications for Layer LS7, addressing issues and improvements identified in [`prompts_LS7.md`](prompts_LS7.md), based on feedback from [`reflection_LS6.md`](reflection_LS6.md) and [`scores_LS6.json`](scores_LS6.json). The primary goals for LS7 include:
- Ensuring comprehensive test coverage for public helper functions in `vectorized_georef.py`.
- Optimizing DSM intersection in `vectorized_georef.py` with full vectorization.
- Increasing overall project test coverage to at least 50%.
- Improving error handling for configuration parameters.
- Implementing missing test cases from LS6.
- Reviewing and refactoring potential redundancies in helper functions.
- Standardizing logger initialization.

## General Test Setup Considerations

-   **Framework**: `pytest` will be used for test structure, fixtures, and execution.
-   **Mocking**: `pytest-mock` (e.g., `mocker.patch`, `mocker.MagicMock`) will be used for external dependencies and functions not directly under test.
-   **Test Data**: Input data should cover valid scenarios, edge cases (e.g., empty inputs, single element inputs, boundaries), and error conditions.
-   **Fixtures**: `pytest` fixtures should be utilized for common setup (e.g., sample data, configurations, mock objects) and teardown.
-   **Numerical Comparisons**: `numpy.testing.assert_allclose` will be used for comparing floating-point numbers and arrays, with appropriate tolerances.
-   **Assertions**: Tests should follow the Arrange-Act-Assert (AAA) pattern. Assertions should be specific and verify all expected outcomes.
-   **Error Handling**: Custom exceptions (e.g., `PipelineConfigError`, `PoseTransformationError`, `VectorizedProcessingError` from [`pipeline_exceptions.py`](pipeline_exceptions.py)) should be tested.

---

## LS7_1: Test Coverage for `vectorized_georef.py` Public Helper Functions

### Objective
Ensure comprehensive test coverage for all public helper functions in `vectorized_georef.py` and verify the 89% coverage claim for this module. This addresses [`reflection_LS6.md#Issue-1`](reflection_LS6.md:14-26).

### Test Cases for Public Helper Functions in `vectorized_georef.py`
(To be added to `test_georeferencing.py` or a new `test_vectorized_georef.py`)

#### Test Case 1.1.1: `calculate_sensor_view_vectors_vectorized` - Valid Inputs
-   **Description**: Verify correct generation of sensor view vectors (normalized) in the sensor frame for a typical number of pixels.
-   **Inputs**:
    -   `num_samples`: Integer (e.g., 5 pixels).
    -   `IFOV_x_rad`, `IFOV_y_rad`: Float, instantaneous fields of view.
    -   `boresight_vector_sensor`: NumPy array (3,), e.g., `np.array([0.0, 0.0, 1.0])`.
-   **Actions**: Call `calculate_sensor_view_vectors_vectorized(num_samples, IFOV_x_rad, IFOV_y_rad, boresight_vector_sensor)`.
-   **Expected Outputs/Behavior**:
    -   Returns a NumPy array of shape (`num_samples`, 3).
    -   All returned vectors are normalized (unit length).
    -   The center pixel's vector (for odd `num_samples`) aligns with the normalized `boresight_vector_sensor`.
    -   Outer pixel vectors are correctly angled based on IFOV.
-   **Test Scaffolding (pytest)**:
    ```python
    import numpy as np
    # from vectorized_georef import calculate_sensor_view_vectors_vectorized

    def test_calculate_sensor_view_vectors_valid_inputs():
        # Arrange
        num_samples = 5
        IFOV_x_rad = 0.01  # Radians
        IFOV_y_rad = 0.01  # Radians
        boresight_vector_sensor = np.array([0.0, 0.0, 1.0])
        
        # Expected properties (example for center pixel)
        expected_center_vector = boresight_vector_sensor / np.linalg.norm(boresight_vector_sensor)

        # Act
        # view_vectors = calculate_sensor_view_vectors_vectorized(
        #     num_samples, IFOV_x_rad, IFOV_y_rad, boresight_vector_sensor
        # )

        # Assert
        # assert view_vectors.shape == (num_samples, 3)
        # norms = np.linalg.norm(view_vectors, axis=1)
        # np.testing.assert_allclose(norms, np.ones(num_samples), atol=1e-7, err_msg="Vectors are not normalized")
        # np.testing.assert_allclose(view_vectors[num_samples // 2], expected_center_vector, atol=1e-7)
        # TODO: Add assertions for edge pixel angles if precise calculation is feasible for test
        pass # Placeholder
    ```

#### Test Case 1.1.2: `calculate_sensor_view_vectors_vectorized` - Edge Case (1 pixel)
-   **Description**: Verify behavior with a single pixel.
-   **Inputs**: `num_samples = 1`, valid `IFOV_x_rad`, `IFOV_y_rad`, `boresight_vector_sensor`.
-   **Actions**: Call `calculate_sensor_view_vectors_vectorized(...)`.
-   **Expected Outputs/Behavior**:
    -   Returns a NumPy array of shape (1, 3).
    -   The single vector aligns with the normalized `boresight_vector_sensor`.
-   **Test Scaffolding (pytest)**:
    ```python
    # from vectorized_georef import calculate_sensor_view_vectors_vectorized

    def test_calculate_sensor_view_vectors_single_pixel():
        # Arrange
        num_samples = 1
        IFOV_x_rad = 0.01
        IFOV_y_rad = 0.01
        boresight_vector_sensor = np.array([0.0, 0.0, 1.0])
        expected_vector = boresight_vector_sensor / np.linalg.norm(boresight_vector_sensor)

        # Act
        # view_vectors = calculate_sensor_view_vectors_vectorized(
        #     num_samples, IFOV_x_rad, IFOV_y_rad, boresight_vector_sensor
        # )

        # Assert
        # assert view_vectors.shape == (1, 3)
        # np.testing.assert_allclose(np.linalg.norm(view_vectors, axis=1), np.array([1.0]), atol=1e-7)
        # np.testing.assert_allclose(view_vectors[0], expected_vector, atol=1e-7)
        pass # Placeholder
    ```

#### Test Case 1.2.1: `transform_to_world_coordinates_vectorized` - Valid Inputs
-   **Description**: Verify correct transformation of sensor view vectors to world coordinates.
-   **Inputs**:
    -   `V_sensor_normalized`: NumPy array (`num_pixels`, 3) of normalized vectors in sensor frame.
    -   `R_body_to_world_line`: NumPy array (3, 3) for IMU body to world rotation (single pose for the line).
    -   `R_sensor_to_body`: NumPy array (3, 3) for sensor to IMU body rotation.
-   **Actions**: Call `transform_to_world_coordinates_vectorized(V_sensor_normalized, R_body_to_world_line, R_sensor_to_body)`.
-   **Expected Outputs/Behavior**:
    -   Returns a NumPy array (`num_pixels`, 3) of vectors transformed to the world frame.
    -   Correct transformation for known rotations (e.g., identity, 90-degree rotations).
-   **Test Scaffolding (pytest)**:
    ```python
    # from vectorized_georef import transform_to_world_coordinates_vectorized

    def test_transform_to_world_coordinates_vectorized_identity_rotations():
        # Arrange
        V_sensor_normalized = np.array([[0.0, 0.0, 1.0], [1.0, 0.0, 0.0]]) # Example vectors
        R_body_to_world_line = np.eye(3)
        R_sensor_to_body = np.eye(3)
        
        # Expected: V_world should be same as V_sensor if rotations are identity
        expected_V_world = V_sensor_normalized.copy()

        # Act
        # V_world = transform_to_world_coordinates_vectorized(
        #     V_sensor_normalized, R_body_to_world_line, R_sensor_to_body
        # )

        # Assert
        # assert V_world.shape == V_sensor_normalized.shape
        # np.testing.assert_allclose(V_world, expected_V_world, atol=1e-7)
        pass # Placeholder
    ```

#### Test Case 1.2.2: `transform_to_world_coordinates_vectorized` - Known Rotation
-   **Description**: Verify transformation with a known non-identity rotation.
-   **Inputs**:
    -   `V_sensor_normalized`: e.g., `np.array([[0.0, 0.0, 1.0]])` (boresight).
    -   `R_body_to_world_line`: e.g., 90-degree rotation around Z-axis.
    -   `R_sensor_to_body`: e.g., identity or another known rotation.
-   **Actions**: Call `transform_to_world_coordinates_vectorized(...)`.
-   **Expected Outputs/Behavior**: Output vector matches manually calculated transformed vector.
-   **Test Scaffolding (pytest)**:
    ```python
    # from vectorized_georef import transform_to_world_coordinates_vectorized

    def test_transform_to_world_coordinates_vectorized_known_rotation():
        # Arrange
        V_sensor_normalized = np.array([[0.0, 0.0, 1.0]]) # Boresight along sensor Z
        
        # R_body_to_world_line: 90-deg rotation around world Z-axis
        # (cos(pi/2) -sin(pi/2) 0)   (0 -1  0)
        # (sin(pi/2)  cos(pi/2) 0) = (1  0  0)
        # (0          0         1)   (0  0  1)
        R_body_to_world_line = np.array([[0.0, -1.0, 0.0], [1.0, 0.0, 0.0], [0.0, 0.0, 1.0]])
        R_sensor_to_body = np.eye(3) # Sensor frame aligned with body frame
        
        # Expected: if sensor Z points to body Z, and body rotates 90 deg around world Z,
        # the vector in world frame should still be [0,0,1] if sensor Z aligns with world Z initially.
        # If sensor Z is [0,0,1], R_sensor_to_body is I, V_body = [0,0,1].
        # V_world = R_body_to_world @ V_body = [0,0,1] @ R_body_to_world_line.T (if R is active)
        # V_world = R_body_to_world_line @ V_body = [0,0,1]
        expected_V_world = np.array([[0.0, 0.0, 1.0]])

        # Act
        # V_world = transform_to_world_coordinates_vectorized(
        #     V_sensor_normalized, R_body_to_world_line, R_sensor_to_body
        # )

        # Assert
        # np.testing.assert_allclose(V_world, expected_V_world, atol=1e-7)
        pass # Placeholder
    ```

#### Test Case 1.3.1: `calculate_flat_plane_intersections_vectorized` - Valid Intersections
-   **Description**: Verify correct calculation of ray intersections with a horizontal plane for nadir and oblique pointing rays.
-   **Inputs**:
    -   `P_sensor_world_line_expanded`: NumPy array (`num_pixels`, 3) of sensor positions in world frame.
    -   `V_world_normalized`: NumPy array (`num_pixels`, 3) of normalized direction vectors in world frame.
    -   `Z_ground_flat_plane`: Scalar float, altitude of the horizontal plane.
-   **Actions**: Call `calculate_flat_plane_intersections_vectorized(P_sensor_world_line_expanded, V_world_normalized, Z_ground_flat_plane)`.
-   **Expected Outputs/Behavior**:
    -   Returns tuple `(X_ground, Y_ground, Z_ground)`, each a NumPy array (`num_pixels`,).
    -   Intersection coordinates `X_ground`, `Y_ground` are correct.
    -   `Z_ground` matches `Z_ground_flat_plane` for all valid intersections.
-   **Test Scaffolding (pytest)**:
    ```python
    # from vectorized_georef import calculate_flat_plane_intersections_vectorized

    def test_calculate_flat_plane_intersections_nadir_pointing():
        # Arrange
        P_sensor_world = np.array([[0.0, 0.0, 100.0], [10.0, 20.0, 50.0]]) # Two sensor positions
        V_world_normalized = np.array([[0.0, 0.0, -1.0], [0.0, 0.0, -1.0]]) # Nadir pointing
        Z_ground_flat_plane = 0.0
        
        expected_X_ground = np.array([0.0, 10.0])
        expected_Y_ground = np.array([0.0, 20.0])
        expected_Z_ground = np.array([0.0, 0.0])

        # Act
        # X_g, Y_g, Z_g = calculate_flat_plane_intersections_vectorized(
        #     P_sensor_world, V_world_normalized, Z_ground_flat_plane
        # )

        # Assert
        # np.testing.assert_allclose(X_g, expected_X_ground, atol=1e-7)
        # np.testing.assert_allclose(Y_g, expected_Y_ground, atol=1e-7)
        # np.testing.assert_allclose(Z_g, expected_Z_ground, atol=1e-7)
        pass # Placeholder
    ```

#### Test Case 1.3.2: `calculate_flat_plane_intersections_vectorized` - Edge Cases (No Intersection)
-   **Description**: Verify handling of rays parallel to the plane or pointing away from it.
-   **Inputs**:
    -   `P_sensor_world_line_expanded`, `V_world_normalized` representing:
        -   Ray parallel to the plane (e.g., `V_world_normalized = [1,0,0]`, `P_sensor_world = [0,0,100]`, `Z_ground = 0`).
        -   Ray pointing upwards away from the plane (e.g., `V_world_normalized = [0,0,1]`, `P_sensor_world = [0,0,100]`, `Z_ground = 0`).
-   **Actions**: Call `calculate_flat_plane_intersections_vectorized(...)`.
-   **Expected Outputs/Behavior**:
    -   `X_ground`, `Y_ground`, `Z_ground` contain `np.nan` for pixels with no valid intersection.
-   **Test Scaffolding (pytest)**:
    ```python
    # from vectorized_georef import calculate_flat_plane_intersections_vectorized

    def test_calculate_flat_plane_intersections_parallel_ray():
        # Arrange
        P_sensor_world = np.array([[0.0, 0.0, 100.0]])
        V_world_normalized = np.array([[1.0, 0.0, 0.0]]) # Parallel to Z=0 plane
        Z_ground_flat_plane = 0.0

        # Act
        # X_g, Y_g, Z_g = calculate_flat_plane_intersections_vectorized(
        #     P_sensor_world, V_world_normalized, Z_ground_flat_plane
        # )

        # Assert
        # assert np.isnan(X_g[0])
        # assert np.isnan(Y_g[0])
        # assert np.isnan(Z_g[0])
        pass # Placeholder

    def test_calculate_flat_plane_intersections_ray_pointing_away():
        # Arrange
        P_sensor_world = np.array([[0.0, 0.0, 100.0]])
        V_world_normalized = np.array([[0.0, 0.0, 1.0]]) # Pointing up, away from Z=0 plane below
        Z_ground_flat_plane = 0.0

        # Act
        # X_g, Y_g, Z_g = calculate_flat_plane_intersections_vectorized(
        #     P_sensor_world, V_world_normalized, Z_ground_flat_plane
        # )

        # Assert
        # assert np.isnan(X_g[0])
        # assert np.isnan(Y_g[0])
        # assert np.isnan(Z_g[0])
        pass # Placeholder
    ```

### Acceptance Criteria for LS7_1
1.  All 8 new tests for `vectorized_georef.py` from `test_specs_LS6.md` (Test Cases 2.1-2.7, primarily targeting internal helpers and `process_hsi_line_vectorized`) are confirmed to be present and correctly implemented.
2.  New dedicated unit tests for public helper functions (`calculate_sensor_view_vectors_vectorized`, `transform_to_world_coordinates_vectorized`, `calculate_flat_plane_intersections_vectorized`) are implemented as specified above, covering valid inputs, edge cases, and error conditions.
3.  A coverage tool (e.g., `pytest-cov`) confirms that `vectorized_georef.py` achieves at least 89% line coverage.
4.  Tests are granular and test each public helper function independently.
5.  Increased confidence in the correctness and maintainability of `vectorized_georef.py`.

---

## LS7_2: Vectorized DSM Intersection in `process_hsi_line_vectorized`

### Objective
Optimize the georeferencing process by implementing fully vectorized DSM intersection within the `process_hsi_line_vectorized` function in `vectorized_georef.py`. This addresses [`reflection_LS6.md#Optimization-Opportunities`](reflection_LS6.md:113).

### Test Cases for `process_hsi_line_vectorized` (DSM Path)
(To be added/modified in `test_georeferencing.py` or `test_vectorized_georef.py`)

#### Test Case 2.1: Vectorized DSM Intersection - Correctness
-   **Description**: Verify that the refactored `process_hsi_line_vectorized` correctly calculates ground intersection points using a vectorized DSM approach.
-   **Inputs**:
    -   `line_index`, `pose_data` (dictionary with raw pose components).
    -   `num_samples`, `IFOV_x_rad`, `IFOV_y_rad`, `R_sensor_to_body`, `lever_arm_sensor_body`, `boresight_vector_sensor`.
    -   `z_ground_method="dsm_intersection"`.
    -   `dsm_interpolator`: A mocked or simple (e.g., flat plane) 2D interpolator object (e.g., `scipy.interpolate.RegularGridInterpolator` or a custom mock).
    -   `dsm_x_coords`, `dsm_y_coords`: Coordinates for the DSM grid.
    -   `d_world_z_threshold`: Threshold for iterative DSM intersection (if applicable to the new vectorized method).
-   **Actions**: Call `process_hsi_line_vectorized(...)` with DSM parameters.
-   **Expected Outputs/Behavior**:
    -   Returns a `List[Dict]` of georeferenced points.
    -   Intersection points are consistent with the DSM data provided by the interpolator.
    -   The implementation must not use per-pixel loops for DSM sampling/intersection.
    -   Results should be comparable to a non-vectorized (but correct) DSM intersection for a small, verifiable scenario.
-   **Test Scaffolding (pytest)**:
    ```python
    import numpy as np
    # from scipy.interpolate import RegularGridInterpolator # If using for mock
    # from vectorized_georef import process_hsi_line_vectorized
    # from unittest.mock import MagicMock

    def test_process_hsi_line_vectorized_dsm_correctness(mocker):
        # Arrange
        line_idx = 0
        num_pix = 3
        # ... (setup pose_data, sensor_model params as in flat-plane tests) ...
        pose = {'pos_x': np.array([0.0]), 'pos_y': np.array([0.0]), 'pos_z': np.array([100.0]),
                  'quat_w': np.array([1.0]), 'quat_x': np.array([0.0]), 
                  'quat_y': np.array([0.0]), 'quat_z': np.array([0.0])}
        IFOV_x_rad, IFOV_y_rad = 0.01, 0.01
        R_sensor_to_body = np.eye(3)
        lever_arm_sensor_body = np.zeros(3)
        boresight_vector_sensor = np.array([0.0, 0.0, 1.0])
        d_world_z_threshold = 0.1

        # Mock DSM: A simple flat plane at Z=10m for easy verification
        dsm_x = np.array([-100, 0, 100])
        dsm_y = np.array([-100, 0, 100])
        dsm_z_values = np.full((3, 3), 10.0) # Flat DSM at Z=10
        
        # mock_interpolator = RegularGridInterpolator((dsm_y, dsm_x), dsm_z_values, method="linear", bounds_error=False, fill_value=np.nan)
        # For more control, use MagicMock
        mock_interpolator = mocker.MagicMock() 
        # Define behavior for mock_interpolator, e.g., for points (xi, yi), return Z_values
        # Example: for nadir view from (0,0,100), intersection should be (0,0,10)
        # This mock needs to accept an array of (y,x) points and return an array of Z values.
        def side_effect_interpolator(points_yx):
            # points_yx is expected to be an array of shape (N, 2)
            # For simplicity, assume all points hit the DSM and return 10.0
            return np.full(points_yx.shape[0], 10.0)
        mock_interpolator.side_effect = side_effect_interpolator
        
        # Act
        # results = process_hsi_line_vectorized(
        #     line_index=line_idx, pose_data=pose, num_samples=num_pix,
        #     IFOV_x_rad=IFOV_x_rad, IFOV_y_rad=IFOV_y_rad,
        #     R_sensor_to_body=R_sensor_to_body,
        #     lever_arm_sensor_body=lever_arm_sensor_body,
        #     boresight_vector_sensor=boresight_vector_sensor,
        #     z_ground_method="dsm_intersection", z_ground_flat_plane=None,
        #     dsm_interpolator=mock_interpolator,
        #     dsm_x_coords=dsm_x, dsm_y_coords=dsm_y, # May not be directly used if interpolator is main interface
        #     d_world_z_threshold=d_world_z_threshold
        # )

        # Assert
        # assert len(results) == num_pix
        # For nadir view from (0,0,100) and flat DSM at Z=10:
        # center_pixel_res = results[num_pix // 2]
        # np.testing.assert_allclose(center_pixel_res['X_ground'], 0.0, atol=0.1) 
        # np.testing.assert_allclose(center_pixel_res['Y_ground'], 0.0, atol=0.1)
        # np.testing.assert_allclose(center_pixel_res['Z_ground'], 10.0, atol=1e-7)
        # mock_interpolator.assert_called_once() # Or more, depending on internal strategy
        # Ensure the points passed to interpolator are as expected.
        pass # Placeholder
    ```

#### Test Case 2.2: Vectorized DSM Intersection - Edge Case (No Intersection / Ray Misses DSM Extent)
-   **Description**: Verify behavior when rays do not intersect the DSM (e.g., point to sky, or point outside horizontal DSM bounds if `fill_value=np.nan` is used for interpolator).
-   **Inputs**: Similar to 2.1, but configure `pose_data` or `dsm_interpolator` such that some/all rays miss.
    -   Mock `dsm_interpolator` to return `np.nan` for certain areas or for all points.
-   **Actions**: Call `process_hsi_line_vectorized(...)`.
-   **Expected Outputs/Behavior**:
    -   `X_ground`, `Y_ground`, `Z_ground` are `np.nan` for pixels with no valid DSM intersection.
-   **Test Scaffolding (pytest)**:
    ```python
    # from vectorized_georef import process_hsi_line_vectorized
    # from unittest.mock import MagicMock

    def test_process_hsi_line_vectorized_dsm_no_intersection(mocker):
        # Arrange
        # ... (setup similar to 2.1) ...
        mock_interpolator = mocker.MagicMock()
        mock_interpolator.side_effect = lambda points_yx: np.full(points_yx.shape[0], np.nan) # All rays miss

        # Act
        # results = process_hsi_line_vectorized(...) # with mock_interpolator

        # Assert
        # for res_item in results:
        #     assert np.isnan(res_item['X_ground'])
        #     assert np.isnan(res_item['Y_ground'])
        #     assert np.isnan(res_item['Z_ground'])
        pass # Placeholder
    ```

#### Test Case 2.3: Vectorized DSM Intersection - Edge Case (Outside DSM Vertical Range / Ray points away after initial estimate)
-   **Description**: Verify behavior if the iterative DSM intersection (if used) fails to converge or ray points away from DSM surface.
-   **Inputs**: Configure DSM and ray geometry such that intersection is problematic.
-   **Actions**: Call `process_hsi_line_vectorized(...)`.
-   **Expected Outputs/Behavior**: Graceful handling, likely `np.nan` for affected pixels.
-   **Test Scaffolding (pytest)**:
    ```python
    # from vectorized_georef import process_hsi_line_vectorized

    def test_process_hsi_line_vectorized_dsm_problematic_intersection(mocker):
        # Arrange: Setup a scenario where rays might point away from a complex DSM surface
        # or where the iterative solution (if any) might struggle.
        # This might involve a more complex mock_interpolator.
        # ...
        
        # Act
        # results = process_hsi_line_vectorized(...)

        # Assert
        # Check for NaNs or expected error handling for problematic pixels.
        pass # Placeholder
    ```

#### Test Case 2.4: Vectorized DSM Intersection - Performance Benchmark
-   **Description**: Measure and compare performance of the new vectorized DSM intersection against a baseline (e.g., the previous per-pixel DSM approach if available, or flat-plane vectorized).
-   **Inputs**: Representative `pose_data`, `num_samples` (e.g., 1000-2000 pixels), realistic mock DSM interpolator.
-   **Actions**: Use `pytest-benchmark` to execute `process_hsi_line_vectorized` with DSM method.
-   **Expected Outputs/Behavior**: Benchmark results recorded. Significant speedup expected compared to per-pixel DSM.
-   **Test Scaffolding (pytest with `pytest-benchmark`)**:
    ```python
    import pytest
    # from vectorized_georef import process_hsi_line_vectorized
    # from unittest.mock import MagicMock

    @pytest.fixture
    def benchmark_dsm_vectorized_data(mocker):
        # Setup similar to benchmark_flat_plane_data from test_specs_LS6.md, but for DSM
        num_pix = 1024
        pose = {'pos_x': np.random.rand(1) * 1000, 'pos_y': np.random.rand(1) * 1000, 
                  'pos_z': np.random.rand(1) * 500 + 50,
                  'quat_w': np.array([1.0]), 'quat_x': np.array([0.0]), 
                  'quat_y': np.array([0.0]), 'quat_z': np.array([0.0])}
        
        mock_interpolator = mocker.MagicMock()
        mock_interpolator.side_effect = lambda points_yx: np.random.rand(points_yx.shape[0]) * 20 # Mock DSM heights
        
        return {
            "line_index": 0, "pose_data": pose, "num_samples": num_pix,
            "IFOV_x_rad": 0.0005, "IFOV_y_rad": 0.0005,
            "R_sensor_to_body": np.eye(3), "lever_arm_sensor_body": np.zeros(3),
            "boresight_vector_sensor": np.array([0.0, 0.0, 1.0]),
            "z_ground_method": "dsm_intersection", "z_ground_flat_plane": None,
            "dsm_interpolator": mock_interpolator, 
            "dsm_x_coords": np.linspace(-1000, 1000, 100), # Dummy coords
            "dsm_y_coords": np.linspace(-1000, 1000, 100), # Dummy coords
            "d_world_z_threshold": 0.1
        }

    # @pytest.mark.benchmark(group="vectorized_dsm_processing")
    def test_benchmark_process_hsi_line_vectorized_dsm(benchmark, benchmark_dsm_vectorized_data):
        # Act
        # results = benchmark(process_hsi_line_vectorized, **benchmark_dsm_vectorized_data)
        # Assert
        # assert results is not None
        # assert len(results) == benchmark_dsm_vectorized_data["num_samples"]
        pass # Placeholder
    ```

### Acceptance Criteria for LS7_2
1.  DSM intersection logic in `process_hsi_line_vectorized` is fully vectorized, avoiding per-pixel loops.
2.  New/updated unit tests validate the correctness of vectorized DSM intersection, including edge cases.
3.  Performance benchmarks show a significant improvement in processing time for DSM method compared to any previous per-pixel approach (if a baseline exists) or relative to flat-plane processing time.
4.  The solution improves `resource_usage_score` and `scalability_score`.
5.  Accuracy of georeferenced points is maintained or improved.

---

## LS7_3: Increase Overall Project Test Coverage to 50%

### Objective
Increase overall project test coverage to at least 50% by adding unit tests to modules with currently low or missing coverage. This addresses [`scores_LS6.json`](scores_LS6.json) (overall_line_coverage_reported_estimate: 37).

### Test Cases for `synchronize_hsi_webodm.py`
(Create `test_synchronize_hsi_webodm.py`)

#### Test Case 3.1.1: `find_closest_timestamp_idx` - Basic Functionality
-   **Description**: Test finding the closest timestamp index in a sorted list.
-   **Inputs**:
    -   `target_timestamp`: float.
    -   `timestamp_list`: sorted list of floats.
-   **Actions**: Call `find_closest_timestamp_idx(target_timestamp, timestamp_list)`.
-   **Expected Outputs/Behavior**: Correct index returned for exact match, closest match, and edge cases (target before start, after end).
-   **Test Scaffolding (pytest)**:
    ```python
    # In test_synchronize_hsi_webodm.py
    # from synchronize_hsi_webodm import find_closest_timestamp_idx # Assuming this function exists

    def test_find_closest_timestamp_idx_exact_match():
        # Arrange
        # target_timestamp = 10.0
        # timestamp_list = [5.0, 10.0, 15.0]
        # expected_idx = 1
        # Act
        # idx = find_closest_timestamp_idx(target_timestamp, timestamp_list)
        # Assert
        # assert idx == expected_idx
        pass # Placeholder
    ```

#### Test Case 3.1.2: `synchronize_data` - Core Logic (Simplified)
-   **Description**: Test the core synchronization logic with mock data.
-   **Inputs**: Mocked HSI sync data, mocked WebODM poses data.
-   **Actions**: Call `synchronize_data(mock_hsi_sync_df, mock_webodm_poses_df, time_threshold)`.
-   **Expected Outputs/Behavior**: Returns a DataFrame with correctly matched pairs based on timestamp proximity and threshold.
-   **Test Scaffolding (pytest)**:
    ```python
    import pandas as pd
    # from synchronize_hsi_webodm import synchronize_data

    def test_synchronize_data_basic_matching():
        # Arrange
        # hsi_sync_data = pd.DataFrame({'hsi_timestamp': [10.0, 20.1, 30.0], 'hsi_file': ['f1', 'f2', 'f3']})
        # webodm_poses_data = pd.DataFrame({'webodm_timestamp': [10.05, 25.0, 30.01], 'pose_data': ['p1', 'p2', 'p3']})
        # time_threshold = 0.1
        # Expected: (f1,p1), (f3,p3)
        # Act
        # synced_df = synchronize_data(hsi_sync_data, webodm_poses_data, time_threshold)
        # Assert
        # assert len(synced_df) == 2
        # assert 'hsi_file' in synced_df.columns and 'pose_data' in synced_df.columns
        pass # Placeholder
    ```

### Test Cases for `compare_timestamps.py`
(Create `test_compare_timestamps.py`)

#### Test Case 3.2.1: `parse_timestamp_from_filename` - Various Formats
-   **Description**: Test parsing timestamps from filenames with different expected formats.
-   **Inputs**: Filenames like "YYYY-MM-DD_HH-MM-SS_cont.img", "prefix_YYYYMMDDHHMMSS_suffix.jpg".
-   **Actions**: Call `parse_timestamp_from_filename(filename, regex_pattern)`.
-   **Expected Outputs/Behavior**: Correct datetime object or timestamp float returned, or None/error for non-matching.
-   **Test Scaffolding (pytest)**:
    ```python
    # In test_compare_timestamps.py
    # from compare_timestamps import parse_timestamp_from_filename # Assuming this function

    def test_parse_timestamp_from_filename_valid():
        # Arrange
        # filename = "2025-06-03_10-30-00_cont.img"
        # pattern = r"(\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2})" # Example pattern
        # expected_datetime_str = "2025-06-03 10:30:00"
        # Act
        # dt_obj = parse_timestamp_from_filename(filename, pattern) # Or timestamp float
        # Assert
        # assert dt_obj is not None
        # assert dt_obj.strftime("%Y-%m-%d %H:%M:%S") == expected_datetime_str
        pass # Placeholder
    ```

### Test Cases for `logging_config.py`
(Create `test_logging_config.py`)

#### Test Case 3.3.1: `setup_logging` - Configuration Application
-   **Description**: Verify that `setup_logging` correctly configures the root logger with specified level and handlers.
-   **Inputs**: `log_level` (e.g., "INFO", "DEBUG"), `log_file` (path to a temporary log file).
-   **Actions**: Call `setup_logging(log_level, log_file)`.
-   **Expected Outputs/Behavior**:
    -   Root logger level is set.
    -   File handler is added and configured (check by logging a message and verifying file content).
    -   Stream handler is added and configured.
-   **Test Scaffolding (pytest)**:
    ```python
    import logging
    import os
    # from logging_config import setup_logging, get_logger

    def test_setup_logging_applies_config(tmp_path):
        # Arrange
        log_file_path = tmp_path / "test_pipeline.log"
        log_level_to_set = "DEBUG"
        
        # Act
        # setup_logging(log_level=log_level_to_set, log_file=str(log_file_path))
        # test_logger = get_logger("my_test_module") # Get a logger instance
        # test_logger.debug("This is a debug message for setup_logging test.")
        # test_logger.info("This is an info message for setup_logging test.")

        # Assert
        # root_logger = logging.getLogger()
        # assert root_logger.level == logging.DEBUG
        # assert os.path.exists(log_file_path)
        # with open(log_file_path, 'r') as f:
        #     log_content = f.read()
        #     assert "This is a debug message" in log_content
        #     assert "This is an info message" in log_content
        
        # Clean up handlers for other tests if necessary (tricky with root logger)
        # logging.getLogger().handlers = [] 
        pass # Placeholder
    ```

#### Test Case 3.3.2: `get_logger` - Logger Retrieval
-   **Description**: Verify `get_logger` returns a logger instance with the correct name.
-   **Inputs**: `logger_name` (string).
-   **Actions**: Call `get_logger(logger_name)`.
-   **Expected Outputs/Behavior**: Returns a `logging.Logger` instance whose name matches `logger_name`.
-   **Test Scaffolding (pytest)**:
    ```python
    # from logging_config import get_logger
    import logging

    def test_get_logger_retrieval():
        # Arrange
        # logger_name = "test.module.special"
        # Act
        # logger_instance = get_logger(logger_name)
        # Assert
        # assert isinstance(logger_instance, logging.Logger)
        # assert logger_instance.name == logger_name
        pass # Placeholder
    ```

### Acceptance Criteria for LS7_3
1.  New unit tests are created for previously untested/undertested modules like [`synchronize_hsi_webodm.py`](synchronize_hsi_webodm.py), [`compare_timestamps.py`](compare_timestamps.py), [`create_georeferenced_rgb.py`](create_georeferenced_rgb.py), [`pipeline_exceptions.py`](pipeline_exceptions.py) (if logic exists), and [`logging_config.py`](logging_config.py).
2.  Tests cover public functions/classes, including valid inputs, edge cases, and error handling.
3.  Tests follow AAA pattern and are independent.
4.  Overall project line coverage (`overall_line_coverage_reported_estimate`) increases to at least 50%.
5.  Improved robustness and reliability of the newly tested modules.

---

## LS7_4: Error Handling for `z_ground_calculation_method`

### Objective
Modify `run_georeferencing` in [`georeference_hsi_pixels.py`](georeference_hsi_pixels.py) to raise a `PipelineConfigError` for unrecognized `z_ground_calculation_method`. This addresses [`reflection_LS6.md#Issue-5`](reflection_LS6.md:85-95).

### Test Cases for `run_georeferencing` in `georeference_hsi_pixels.py`
(To be added/modified in `test_georeferencing.py`)

#### Test Case 4.1: Invalid `z_ground_calculation_method` - Raise `PipelineConfigError`
-   **Description**: Verify that `run_georeferencing` raises `PipelineConfigError` when an unknown `z_ground_calculation_method` is provided.
-   **Inputs**:
    -   `config` dictionary with `georeferencing_params.z_ground_calculation_method = "unknown_method"`.
    -   Other necessary valid inputs for `run_georeferencing` (mocked HSI data, pose data, etc.).
-   **Actions**: Call `run_georeferencing(...)` within a `pytest.raises` context.
-   **Expected Outputs/Behavior**: `PipelineConfigError` is raised.
-   **Test Scaffolding (pytest)**:
    ```python
    import pytest
    # from georeference_hsi_pixels import run_georeferencing
    # from pipeline_exceptions import PipelineConfigError # Assuming it's defined here
    # ... (other necessary imports for dummy data)

    def test_run_georeferencing_invalid_z_ground_method_raises_error(mocker):
        # Arrange
        # ... (setup minimal hsi_data_list, pose_data_df, sensor_model, lever_arm_params)
        # Example:
        # hsi_data_list = [{'timestamp': 12345.0, 'data': np.random.rand(5,3)}]
        # pose_data_df = pd.DataFrame([{'timestamp': 12345.0, ...}]).set_index('timestamp')
        # sensor_model = {...}
        # lever_arm_params = {...}

        invalid_config = {
            'georeferencing_params': {
                'z_ground_calculation_method': 'this_is_not_a_valid_method',
                'use_vectorized_processing': False # Or True, ensure other paths are okay
            },
            # ... other necessary config sections ...
        }
        # Mock any functions called before the check if necessary
        # mocker.patch('georeference_hsi_pixels.get_dsm_handler', return_value=None) # If DSM path is checked first

        # Act & Assert
        # with pytest.raises(PipelineConfigError):
        #     run_georeferencing(hsi_data_list, pose_data_df, sensor_model, lever_arm_params, invalid_config)
        pass # Placeholder
    ```

#### Test Case 4.2: `PipelineConfigError` Message Content Verification
-   **Description**: Verify the error message of the raised `PipelineConfigError` clearly states the unknown method and lists valid methods.
-   **Inputs**: Same as Test Case 4.1.
-   **Actions**: Call `run_georeferencing(...)` and catch the exception.
-   **Expected Outputs/Behavior**: The exception message string contains the problematic method name and a list of valid options (e.g., "avg_pose_z_minus_offset", "fixed_value", "dsm_intersection").
-   **Test Scaffolding (pytest)**:
    ```python
    # ... (imports as in 4.1) ...

    def test_run_georeferencing_invalid_z_ground_method_error_message(mocker):
        # Arrange
        # ... (setup as in 4.1) ...
        unknown_method_name = 'bad_method_name_123'
        invalid_config = {
            'georeferencing_params': {
                'z_ground_calculation_method': unknown_method_name,
                # ...
            },
            # ...
        }

        # Act & Assert
        # with pytest.raises(PipelineConfigError) as excinfo:
        #     run_georeferencing(hsi_data_list, pose_data_df, sensor_model, lever_arm_params, invalid_config)
        
        # error_message = str(excinfo.value)
        # assert unknown_method_name in error_message
        # assert "avg_pose_z_minus_offset" in error_message
        # assert "fixed_value" in error_message
        # assert "dsm_intersection" in error_message
        pass # Placeholder
    ```

### Acceptance Criteria for LS7_4
1.  `run_georeferencing` raises `PipelineConfigError` for any `z_ground_calculation_method` not in the recognized set.
2.  The error message is informative, including the invalid method provided and listing valid options.
3.  Unit tests in `test_georeferencing.py` verify this behavior.
4.  Improved configuration validation and error reporting.

---

## LS7_5: Implement Missing Test Case for DSM Path Resolution

### Objective
Implement the missing test case `TestLS6DSMPathResolution.test_dsm_path_resolution_relative_file_not_found_at_resolved_path` in [`test_georeferencing.py`](test_georeferencing.py) as specified in [`test_specs_LS6.md:579-611`](test_specs_LS6.md:579-611). This addresses [`reflection_LS6.md#Issue-2`](reflection_LS6.md:27-32).

### Test Case for `TestLS6DSMPathResolution` in `test_georeferencing.py`

#### Test Case 5.1: `test_dsm_path_resolution_relative_file_not_found_at_resolved_path`
-   **Description**: Verify that if a relative DSM path is correctly resolved (relative to config) but the target file does not exist at that resolved location, the appropriate `FileNotFoundError` (or `rasterio.errors.RasterioIOError`) is raised by the underlying file access attempt.
-   **Inputs**:
    -   A temporary `config_file` (e.g., `tmp_dir/config_subdir/my_config.toml`).
    -   `dsm_path` in config is relative (e.g., `"data/non_existent_dsm.tif"`).
    -   The resolved path (e.g., `tmp_dir/config_subdir/data/non_existent_dsm.tif`) does *not* exist.
    -   Mock `Path(resolved_path).exists()` to return `False` if the check is done before attempting to open, or ensure the file truly doesn't exist if `rasterio.open` is called directly.
-   **Actions**: Call the function responsible for DSM loading/handling (e.g., `get_dsm_handler` or `run_georeferencing` configured for DSM use).
-   **Expected Outputs/Behavior**: A `FileNotFoundError` or `rasterio.errors.RasterioIOError` is raised.
-   **Test Scaffolding (pytest)**: (Adapted from [`test_specs_LS6.md:588-611`](test_specs_LS6.md:588-611))
    ```python
    import pytest
    from pathlib import Path
    # import rasterio # For specific error type
    # from georeference_hsi_pixels import get_dsm_handler # Or the relevant function being tested
    # from pipeline_exceptions import PipelineConfigError # If config validation happens first

    # This test would be part of a class like TestLS6DSMPathResolution
    def test_dsm_path_resolution_relative_file_not_found_at_resolved_path(tmp_path, mocker):
        # Arrange
        config_subdir = tmp_path / "config_location"
        config_subdir.mkdir()
        config_file_path = config_subdir / "my_config.toml"
        # Create a dummy config file (content might not matter if path is passed directly)
        with open(config_file_path, "w") as f:
            f.write("[georeferencing_params]\n")
            f.write('dsm_file_path = "terrain_data/ghost_dsm.tif"\n')

        dsm_relative_in_config = "terrain_data/ghost_dsm.tif"
        
        # The directory where it would be, might exist or not. The file itself must not.
        # Path(config_subdir / "terrain_data").mkdir(exist_ok=True) 
        
        # Resolved path should be config_subdir / "terrain_data" / "ghost_dsm.tif"
        # Ensure this path does NOT exist.

        # Option 1: If Path.exists() is checked explicitly before rasterio.open
        # mock_path_obj = mocker.MagicMock(spec=Path)
        # mock_path_obj.exists.return_value = False
        # mock_path_obj.is_file.return_value = False
        # mock_path_constructor = mocker.patch('georeference_hsi_pixels.Path') # Assuming Path is imported there
        # mock_path_constructor.return_value = mock_path_obj
        
        # Option 2: Let rasterio.open fail naturally (preferred if it's the direct opener)
        # No need to mock rasterio.open itself, just ensure the file isn't there.

        # Act & Assert
        # with pytest.raises((FileNotFoundError, rasterio.errors.RasterioIOError)) as excinfo:
        #     # Call the function that attempts to load/use the DSM
        #     # Example:
        #     # get_dsm_handler(
        #     #     dsm_file_path=dsm_relative_in_config,
        #     #     config_file_path=str(config_file_path)
        #     # )
        #     # Or, if it's part of run_georeferencing:
        #     # run_georeferencing(..., config_with_ghost_dsm, ...)
        
        # resolved_non_existent_path = (config_subdir / dsm_relative_in_config).resolve()
        # assert str(resolved_non_existent_path) in str(excinfo.value).lower() # Check if path is in error
        pass # Placeholder
    ```

### Acceptance Criteria for LS7_5
1.  The test method `test_dsm_path_resolution_relative_file_not_found_at_resolved_path` is added to `TestLS6DSMPathResolution` class in `test_georeferencing.py`.
2.  The test correctly sets up a scenario with a relative DSM path in config that resolves to a non-existent file.
3.  The test asserts that the appropriate error (`FileNotFoundError` or `rasterio.errors.RasterioIOError`) is raised.
4.  The test logic aligns with the specifications from [`test_specs_LS6.md:588-611`](test_specs_LS6.md:588-611).

---

## LS7_6: Review Redundancy in `vectorized_georef.py` Helpers

### Objective
Review internal helper functions in `vectorized_georef.py` for redundancy against their public counterparts. Either remove them if redundant or clarify their distinct purpose with documentation and specific tests. This addresses [`reflection_LS6.md#Issue-3`](reflection_LS6.md:33-44).

### Test Cases for Retained Internal Helper Functions (If Any Justified)
If, after review, an internal (`_` prefixed) helper function (e.g., `_calculate_sensor_pixel_vectors_vectorized`, `_transform_vectors_to_world_vectorized`, `_intersect_rays_with_horizontal_plane_vectorized`) is retained because it serves a distinct, necessary purpose not covered by its public counterpart or existing tests for public functions, new tests should be defined here.

#### Example Test Case 6.X.1: `_internal_helper_function_name` - Demonstrating Unique Utility
-   **Description**: Test the specific unique behavior or edge case handling of the retained internal helper function `_internal_helper_function_name` that is not covered by tests for its public counterpart.
-   **Inputs**: Specific inputs that trigger the unique behavior of the internal helper.
-   **Actions**: Call `_internal_helper_function_name(...)`.
-   **Expected Outputs/Behavior**: The output demonstrates the unique utility or handles the specific edge case correctly.
-   **Test Scaffolding (pytest)**:
    ```python
    # In test_vectorized_georef.py (or test_georeferencing.py)
    # from vectorized_georef import _internal_helper_function_name # If retained

    def test_internal_helper_unique_scenario_XYZ():
        # Arrange
        # Setup inputs specific to the unique behavior of _internal_helper_function_name
        # ...

        # Act
        # result = _internal_helper_function_name(...)

        # Assert
        # Assertions that verify the unique behavior or edge case handling.
        # These assertions should be different from those for the public counterpart.
        pass # Placeholder
    ```
*If no internal functions are retained, or if their existing tests (e.g., from `test_specs_LS6.md` Test Cases 2.1-2.4) are deemed sufficient and distinct, this section may state that no new tests are required.*

### Acceptance Criteria for LS7_6
1.  Internal helper functions in `vectorized_georef.py` are reviewed.
2.  Redundant internal helpers are identified for removal (implementation phase).
3.  Retained internal helpers have their distinct purpose clearly documented in the code.
4.  If retained and their unique utility is not covered by existing tests, new specific unit tests are defined (as above) and subsequently implemented.
5.  `vectorized_georef.py` is streamlined and more maintainable.

---

## LS7_7: Logger Initialization in `main_pipeline.py`

### Objective
Refactor `main_pipeline.py` to initialize the logger once at the module level. This addresses [`reflection_LS6.md#Issue-4`](reflection_LS6.md:45-57).

### Test Cases for `main_pipeline.py` (Logging Verification)
(To be added/modified in `test_main_pipeline.py`)
These tests aim to verify that logging still functions correctly after refactoring and that the module-level logger is used.

#### Test Case 7.1: Verify Logger Usage in `load_pipeline_config`
-   **Description**: Ensure `load_pipeline_config` uses the module-level logger after refactoring.
-   **Inputs**: Path to a dummy (or mocked) config file.
-   **Actions**:
    1. Mock the module-level `logger` in `main_pipeline.py` (e.g., `mocker.patch('main_pipeline.logger')`).
    2. Call `main_pipeline.load_pipeline_config(...)`.
-   **Expected Outputs/Behavior**: The mocked module-level logger's methods (e.g., `info`, `error`) are called as expected within `load_pipeline_config`.
-   **Test Scaffolding (pytest)**:
    ```python
    # In test_main_pipeline.py
    # import main_pipeline # To access the module for patching
    # from unittest.mock import MagicMock

    def test_load_pipeline_config_uses_module_logger(mocker, tmp_path):
        # Arrange
        mock_module_logger = mocker.patch('main_pipeline.logger', spec=True) # Patch module-level logger
        
        dummy_config_path = tmp_path / "dummy_config.toml"
        with open(dummy_config_path, "w") as f:
            f.write("[settings]\nkey = 'value'\n") # Minimal valid TOML

        # Act
        # main_pipeline.load_pipeline_config(str(dummy_config_path))

        # Assert
        # mock_module_logger.info.assert_called() # Check if logger.info was called
        # Example: check for a specific log message if applicable
        # mock_module_logger.info.assert_any_call(f"Successfully loaded configuration from: {dummy_config_path}")
        pass # Placeholder
    ```

#### Test Case 7.2: Verify Logger Usage in `run_complete_pipeline`
-   **Description**: Ensure `run_complete_pipeline` uses the module-level logger and `setup_logging` is effective.
-   **Inputs**: Path to a dummy config file.
-   **Actions**:
    1. Mock `main_pipeline.setup_logging`.
    2. Mock the module-level `logger` in `main_pipeline.py`.
    3. Mock other functions called by `run_complete_pipeline` to prevent full execution (e.g., `load_pipeline_config`, `georeference_hsi_pixels.run_georeferencing`).
    4. Call `main_pipeline.run_complete_pipeline(...)`.
-   **Expected Outputs/Behavior**:
    -   `main_pipeline.setup_logging` is called.
    -   The mocked module-level logger's methods are called.
-   **Test Scaffolding (pytest)**:
    ```python
    # In test_main_pipeline.py
    # import main_pipeline

    def test_run_complete_pipeline_uses_module_logger_and_sets_up_logging(mocker, tmp_path):
        # Arrange
        mock_setup_logging = mocker.patch('main_pipeline.setup_logging')
        mock_module_logger_main = mocker.patch('main_pipeline.logger', spec=True)
        
        # Mock functions called within run_complete_pipeline to isolate logger testing
        mock_load_config = mocker.patch('main_pipeline.load_pipeline_config', return_value={'georeferencing_params':{}}) # Min config
        mock_run_georef = mocker.patch('main_pipeline.georeference_hsi_pixels.run_georeferencing', return_value=([], 0))
        # Add mocks for other pipeline steps as needed

        dummy_config_path = tmp_path / "dummy_config.toml"
        dummy_config_path.touch()

        # Act
        # main_pipeline.run_complete_pipeline(str(dummy_config_path))

        # Assert
        # mock_setup_logging.assert_called_once()
        # mock_module_logger_main.info.assert_any_call(f"Starting complete pipeline with config: {dummy_config_path}")
        # mock_load_config.assert_called_once_with(str(dummy_config_path))
        # mock_run_georef.assert_called_once() # Or based on config
        pass # Placeholder
    ```

### Acceptance Criteria for LS7_7
1.  `logger = get_logger(__name__)` is defined once at the module level in `main_pipeline.py`.
2.  Redundant logger initializations within functions are removed.
3.  `setup_logging()` is still called appropriately to configure logging for the application.
4.  Logging continues to function as expected (verified by existing tests if they cover logging, or by new minimal tests like those above).
5.  Improved code style and adherence to standard Python logging practices.
"""
Unit tests for HSI-WebODM synchronization functionality.

This module tests the synchronization logic as specified in LS7_3 requirements.
"""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import patch, MagicMock

# Import the modules to test
from synchronize_hsi_webodm import find_closest_timestamp_idx, run_synchronization


class TestFindClosestTimestampIdx:
    """Test cases for find_closest_timestamp_idx function (LS7_3)."""
    
    def test_find_closest_timestamp_idx_exact_match(self):
        """Test finding exact timestamp match."""
        # Arrange
        target_timestamp = 10.0
        timestamp_list = [5.0, 10.0, 15.0]
        expected_idx = 1
        
        # Act
        idx = find_closest_timestamp_idx(target_timestamp, timestamp_list)
        
        # Assert
        assert idx == expected_idx
    
    def test_find_closest_timestamp_idx_closest_match(self):
        """Test finding closest timestamp when no exact match."""
        # Arrange
        target_timestamp = 10.1
        timestamp_list = [5.0, 10.0, 15.0]
        expected_idx = 1  # 10.0 is closer than 15.0
        
        # Act
        idx = find_closest_timestamp_idx(target_timestamp, timestamp_list)
        
        # Assert
        assert idx == expected_idx
    
    def test_find_closest_timestamp_idx_target_before_start(self):
        """Test behavior when target is before the first timestamp."""
        # Arrange
        target_timestamp = 1.0
        timestamp_list = [5.0, 10.0, 15.0]
        expected_idx = 0  # Should return first index
        
        # Act
        idx = find_closest_timestamp_idx(target_timestamp, timestamp_list)
        
        # Assert
        assert idx == expected_idx
    
    def test_find_closest_timestamp_idx_target_after_end(self):
        """Test behavior when target is after the last timestamp."""
        # Arrange
        target_timestamp = 20.0
        timestamp_list = [5.0, 10.0, 15.0]
        expected_idx = 2  # Should return last index
        
        # Act
        idx = find_closest_timestamp_idx(target_timestamp, timestamp_list)
        
        # Assert
        assert idx == expected_idx
    
    def test_find_closest_timestamp_idx_single_element(self):
        """Test with single element list."""
        # Arrange
        target_timestamp = 10.0
        timestamp_list = [5.0]
        expected_idx = 0
        
        # Act
        idx = find_closest_timestamp_idx(target_timestamp, timestamp_list)
        
        # Assert
        assert idx == expected_idx
    
    def test_find_closest_timestamp_idx_empty_list(self):
        """Test behavior with empty timestamp list."""
        # Arrange
        target_timestamp = 10.0
        timestamp_list = []
        
        # Act & Assert
        with pytest.raises((IndexError, ValueError)):
            find_closest_timestamp_idx(target_timestamp, timestamp_list)


class TestRunSynchronization:
    """Test cases for run_synchronization function (LS7_3)."""
    
    def test_run_synchronization_basic_functionality(self, mocker, tmp_path):
        """Test basic synchronization functionality."""
        # Arrange
        config = {
            'paths': {
                'hsi_data_directory': str(tmp_path / "hsi"),
                'output_directory': str(tmp_path / "output"),
                'hsi_poses_csv': 'synchronized_poses.csv'
            },
            'parameters': {
                'synchronization': {
                    'time_threshold_seconds': 0.1
                }
            }
        }
        
        # Mock HSI sync data
        mock_hsi_sync_df = pd.DataFrame({
            'hsi_timestamp': [10.0, 20.1, 30.0],
            'hsi_file': ['f1.img', 'f2.img', 'f3.img']
        })
        
        # Mock WebODM poses data
        mock_webodm_poses_df = pd.DataFrame({
            'webodm_timestamp': [10.05, 25.0, 30.01],
            'pos_x': [1.0, 2.0, 3.0],
            'pos_y': [4.0, 5.0, 6.0],
            'pos_z': [7.0, 8.0, 9.0]
        })
        
        # Mock file operations
        mocker.patch('synchronize_hsi_webodm.pd.read_csv', side_effect=[mock_hsi_sync_df, mock_webodm_poses_df])
        mocker.patch('synchronize_hsi_webodm.os.makedirs')
        mock_to_csv = mocker.patch('pandas.DataFrame.to_csv')
        
        # Act
        result = run_synchronization(config)
        
        # Assert
        assert result is True
        mock_to_csv.assert_called_once()
    
    def test_run_synchronization_with_time_threshold(self, mocker, tmp_path):
        """Test synchronization respects time threshold."""
        # Arrange
        config = {
            'paths': {
                'hsi_data_directory': str(tmp_path / "hsi"),
                'output_directory': str(tmp_path / "output"),
                'hsi_poses_csv': 'synchronized_poses.csv'
            },
            'parameters': {
                'synchronization': {
                    'time_threshold_seconds': 0.05  # Strict threshold
                }
            }
        }
        
        # Mock data with some matches within threshold and some outside
        mock_hsi_sync_df = pd.DataFrame({
            'hsi_timestamp': [10.0, 20.0, 30.0],
            'hsi_file': ['f1.img', 'f2.img', 'f3.img']
        })
        
        mock_webodm_poses_df = pd.DataFrame({
            'webodm_timestamp': [10.02, 20.1, 30.01],  # Only first and third within threshold
            'pos_x': [1.0, 2.0, 3.0],
            'pos_y': [4.0, 5.0, 6.0],
            'pos_z': [7.0, 8.0, 9.0]
        })
        
        # Mock file operations
        mocker.patch('synchronize_hsi_webodm.pd.read_csv', side_effect=[mock_hsi_sync_df, mock_webodm_poses_df])
        mocker.patch('synchronize_hsi_webodm.os.makedirs')
        mock_to_csv = mocker.patch('pandas.DataFrame.to_csv')
        
        # Act
        result = run_synchronization(config)
        
        # Assert
        assert result is True
        mock_to_csv.assert_called_once()
    
    def test_run_synchronization_missing_config_keys(self, mocker):
        """Test error handling for missing configuration keys."""
        # Arrange
        incomplete_config = {
            'paths': {
                'hsi_data_directory': '/test/hsi'
                # Missing other required keys
            }
        }
        
        # Act & Assert
        with pytest.raises((KeyError, AttributeError)):
            run_synchronization(incomplete_config)
    
    def test_run_synchronization_file_not_found(self, mocker, tmp_path):
        """Test error handling when input files are not found."""
        # Arrange
        config = {
            'paths': {
                'hsi_data_directory': str(tmp_path / "nonexistent"),
                'output_directory': str(tmp_path / "output"),
                'hsi_poses_csv': 'synchronized_poses.csv'
            },
            'parameters': {
                'synchronization': {
                    'time_threshold_seconds': 0.1
                }
            }
        }
        
        # Mock file operations to raise FileNotFoundError
        mocker.patch('synchronize_hsi_webodm.pd.read_csv', side_effect=FileNotFoundError("File not found"))
        
        # Act & Assert
        with pytest.raises(FileNotFoundError):
            run_synchronization(config)


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v"])
